{"testName": "test-runner-2025-07-24T01-47-22", "startTime": "2025-07-24T01:47:22.766Z", "endTime": "2025-07-24T01:47:22.812Z", "duration": 46, "results": {"passed": 0, "failed": 2, "skipped": 0, "total": 2}, "successRate": 0, "errors": [{"level": "ERROR", "message": "System health check failed", "error": {"message": "Request failed with status code 404", "stack": "AxiosError: Request failed with status code 404\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TestRunner.healthCheck (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:62:24)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:31:7)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)", "response": {"success": false, "error": {"code": "NOT_FOUND", "message": "Route GET /api/health not found"}}}, "timestamp": "2025-07-24T01:47:22.811Z"}, {"level": "ERROR", "message": "E2E Test Suite failed", "error": {"message": "Cannot proceed with tests - system not healthy", "stack": "Error: Cannot proceed with tests - system not healthy\n    at TestRunner.healthCheck (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:76:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:31:7)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)"}, "timestamp": "2025-07-24T01:47:22.811Z"}], "logs": [{"level": "INFO", "message": "Starting comprehensive E2E test suite", "data": null, "timestamp": "2025-07-24T01:47:22.767Z"}, {"level": "INFO", "message": "Starting: System Health Check", "data": null, "timestamp": "2025-07-24T01:47:22.767Z"}, {"level": "ERROR", "message": "System health check failed", "error": {"message": "Request failed with status code 404", "stack": "AxiosError: Request failed with status code 404\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TestRunner.healthCheck (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:62:24)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:31:7)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)", "response": {"success": false, "error": {"code": "NOT_FOUND", "message": "Route GET /api/health not found"}}}, "timestamp": "2025-07-24T01:47:22.811Z"}, {"level": "ERROR", "message": "E2E Test Suite failed", "error": {"message": "Cannot proceed with tests - system not healthy", "stack": "Error: Cannot proceed with tests - system not healthy\n    at TestRunner.healthCheck (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:76:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:31:7)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)"}, "timestamp": "2025-07-24T01:47:22.811Z"}]}