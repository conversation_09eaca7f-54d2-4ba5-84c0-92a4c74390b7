{"testName": "single-user-test-2025-07-24T01-22-54", "startTime": "2025-07-24T01:22:54.132Z", "endTime": "2025-07-24T01:22:54.175Z", "duration": 43, "results": {"passed": 1, "failed": 2, "skipped": 0, "total": 3}, "successRate": 33.33, "errors": [{"level": "ERROR", "message": "User registration failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:51:22)\n    at async SingleUserTest.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:69:24)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:27:7)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Email must be a valid email address"]}}}, "timestamp": "2025-07-24T01:22:54.174Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:51:22)\n    at async SingleUserTest.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:69:24)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:27:7)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Email must be a valid email address"]}}}, "timestamp": "2025-07-24T01:22:54.175Z"}], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-24T01:22:54.133Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-24T01:22:54.135Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>@yahoo.com", "username": "testuser_n2dmuu"}, "timestamp": "2025-07-24T01:22:54.137Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-24T01:22:54.138Z"}, {"level": "ERROR", "message": "User registration failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:51:22)\n    at async SingleUserTest.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:69:24)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:27:7)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Email must be a valid email address"]}}}, "timestamp": "2025-07-24T01:22:54.174Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:51:22)\n    at async SingleUserTest.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:69:24)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:27:7)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Email must be a valid email address"]}}}, "timestamp": "2025-07-24T01:22:54.175Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-24T01:22:54.175Z"}]}