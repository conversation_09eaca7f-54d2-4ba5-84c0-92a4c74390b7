{"testName": "dual-user-test-2025-07-24T04-42-22", "startTime": "2025-07-24T04:42:22.156Z", "endTime": "2025-07-24T04:43:02.166Z", "duration": 40010, "results": {"passed": 3, "failed": 3, "skipped": 0, "total": 6}, "successRate": 50, "errors": [{"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:328:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:136:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:81:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:43:02.163Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:101:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:43:02.164Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:101:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:43:02.164Z"}], "logs": [{"level": "INFO", "message": "Starting Dual User E2E Test with 2 users", "data": null, "timestamp": "2025-07-24T04:42:22.156Z"}, {"level": "INFO", "message": "Starting: Step 1: Initialize Users", "data": null, "timestamp": "2025-07-24T04:42:22.158Z"}, {"level": "SUCCESS", "message": "User 1 initialized", "data": {"email": "<EMAIL>", "username": "testuserzig3ba"}, "timestamp": "2025-07-24T04:42:22.161Z"}, {"level": "SUCCESS", "message": "User 2 initialized", "data": {"email": "<EMAIL>", "username": "testuser1aaqu2"}, "timestamp": "2025-07-24T04:42:22.161Z"}, {"level": "INFO", "message": "Starting: Step 2: Run Parallel User Flows", "data": null, "timestamp": "2025-07-24T04:42:22.162Z"}, {"level": "INFO", "message": "User 1: Starting user flow", "data": null, "timestamp": "2025-07-24T04:42:22.168Z"}, {"level": "INFO", "message": "User 1: Registering user", "data": null, "timestamp": "2025-07-24T04:42:22.168Z"}, {"level": "INFO", "message": "User 1: User registered successfully", "data": {"userId": "48a3a873-621a-4de6-8a75-d777f3b6b8b0", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:42:22.259Z"}, {"level": "INFO", "message": "User 1: Logging in user", "data": null, "timestamp": "2025-07-24T04:42:22.259Z"}, {"level": "INFO", "message": "User 1: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:42:22.318Z"}, {"level": "INFO", "message": "User 1: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:42:22.318Z"}, {"level": "INFO", "message": "User 1: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:42:22.335Z"}, {"level": "INFO", "message": "User 1: Updating profile", "data": null, "timestamp": "2025-07-24T04:42:22.335Z"}, {"level": "INFO", "message": "User 1: Profile updated successfully", "data": {"username": "updatedibq70w"}, "timestamp": "2025-07-24T04:42:22.356Z"}, {"level": "INFO", "message": "User 1: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:42:22.356Z"}, {"level": "INFO", "message": "User 1: Assessment submitted successfully", "data": {"jobId": "31144307-7a64-43ef-bba0-23ff8689632d", "status": "queued"}, "timestamp": "2025-07-24T04:42:22.472Z"}, {"level": "INFO", "message": "User 1: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:42:22.472Z"}, {"level": "INFO", "message": "User 2: Starting user flow", "data": null, "timestamp": "2025-07-24T04:42:24.164Z"}, {"level": "INFO", "message": "User 2: Registering user", "data": null, "timestamp": "2025-07-24T04:42:24.164Z"}, {"level": "INFO", "message": "User 2: User registered successfully", "data": {"userId": "5367a0aa-a531-4e69-b5e0-86080060ed70", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:42:24.234Z"}, {"level": "INFO", "message": "User 2: Logging in user", "data": null, "timestamp": "2025-07-24T04:42:24.234Z"}, {"level": "INFO", "message": "User 2: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:42:24.296Z"}, {"level": "INFO", "message": "User 2: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:42:24.296Z"}, {"level": "INFO", "message": "User 2: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:42:24.307Z"}, {"level": "INFO", "message": "User 2: Updating profile", "data": null, "timestamp": "2025-07-24T04:42:24.307Z"}, {"level": "INFO", "message": "User 2: Profile updated successfully", "data": {"username": "updatedfsew89"}, "timestamp": "2025-07-24T04:42:24.329Z"}, {"level": "INFO", "message": "User 2: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:42:24.330Z"}, {"level": "INFO", "message": "User 2: Assessment submitted successfully", "data": {"jobId": "32a3ae3c-5ca6-4d29-8635-c8f03c50c4c4", "status": "queued"}, "timestamp": "2025-07-24T04:42:24.442Z"}, {"level": "INFO", "message": "User 2: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:42:24.443Z"}, {"level": "INFO", "message": "User 1: Assessment completion notification received", "data": {"jobId": "31144307-7a64-43ef-bba0-23ff8689632d", "resultId": "934987ab-74c3-46cc-8e70-6bd6dd2714d5"}, "timestamp": "2025-07-24T04:42:44.005Z"}, {"level": "INFO", "message": "User 1: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:42:44.005Z"}, {"level": "INFO", "message": "User 1: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:42:44.005Z"}, {"level": "INFO", "message": "User 2: Assessment completion notification received", "data": {"jobId": "32a3ae3c-5ca6-4d29-8635-c8f03c50c4c4", "resultId": "14258fde-6988-4cbe-a678-0c974318cd86"}, "timestamp": "2025-07-24T04:42:46.486Z"}, {"level": "INFO", "message": "User 2: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:42:46.487Z"}, {"level": "INFO", "message": "User 2: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:42:46.487Z"}, {"level": "INFO", "message": "User 1: Profile persona retrieved successfully", "data": {"archetype": "The Systematic Leader", "careerCount": 5}, "timestamp": "2025-07-24T04:42:50.031Z"}, {"level": "INFO", "message": "User 1: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:42:50.031Z"}, {"level": "INFO", "message": "User 2: Profile persona retrieved successfully", "data": {"archetype": "The Principled Innovator", "careerCount": 5}, "timestamp": "2025-07-24T04:42:52.507Z"}, {"level": "INFO", "message": "User 2: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:42:52.508Z"}, {"level": "INFO", "message": "User 1: Chatbot conversation created", "data": {"conversationId": "fe4eb274-0cac-4984-a87d-4e109bc1c238"}, "timestamp": "2025-07-24T04:42:55.205Z"}, {"level": "INFO", "message": "User 2: Chatbot conversation created", "data": {"conversationId": "ca5ccaef-76fa-40cb-9bb2-6d8c2c73dfc6"}, "timestamp": "2025-07-24T04:42:57.157Z"}, {"level": "INFO", "message": "User 2: <PERSON><PERSON><PERSON> test failed", "data": null, "timestamp": "2025-07-24T04:43:00.958Z"}, {"level": "INFO", "message": "User 2: User flow failed: Request failed with status code 503", "data": null, "timestamp": "2025-07-24T04:43:00.958Z"}, {"level": "INFO", "message": "User 1: Message 1 sent and responded", "data": null, "timestamp": "2025-07-24T04:43:02.143Z"}, {"level": "INFO", "message": "User 1: Chatbot interaction completed successfully", "data": null, "timestamp": "2025-07-24T04:43:02.143Z"}, {"level": "INFO", "message": "User 1: Cleaning up test account", "data": null, "timestamp": "2025-07-24T04:43:02.143Z"}, {"level": "INFO", "message": "User 1: Test account cleaned up successfully", "data": null, "timestamp": "2025-07-24T04:43:02.163Z"}, {"level": "INFO", "message": "User 1: User flow completed successfully", "data": null, "timestamp": "2025-07-24T04:43:02.163Z"}, {"level": "SUCCESS", "message": "User 1 flow completed successfully", "data": null, "timestamp": "2025-07-24T04:43:02.163Z"}, {"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:328:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:136:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:81:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:43:02.163Z"}, {"level": "INFO", "message": "Parallel execution completed: 1 success, 1 failures", "data": null, "timestamp": "2025-07-24T04:43:02.164Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:101:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:43:02.164Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:101:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:43:02.164Z"}, {"level": "INFO", "message": "Disconnected all users from services", "data": null, "timestamp": "2025-07-24T04:43:02.166Z"}]}