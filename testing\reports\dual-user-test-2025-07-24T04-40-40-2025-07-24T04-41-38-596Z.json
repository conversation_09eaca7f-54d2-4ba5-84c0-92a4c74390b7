{"testName": "dual-user-test-2025-07-24T04-40-40", "startTime": "2025-07-24T04:40:40.946Z", "endTime": "2025-07-24T04:41:38.595Z", "duration": 57649, "results": {"passed": 3, "failed": 3, "skipped": 0, "total": 6}, "successRate": 50, "errors": [{"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:316:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:129:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:74:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:41:38.592Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:41:38.593Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:41:38.593Z"}], "logs": [{"level": "INFO", "message": "Starting Dual User E2E Test with 2 users", "data": null, "timestamp": "2025-07-24T04:40:40.946Z"}, {"level": "INFO", "message": "Starting: Step 1: Initialize Users", "data": null, "timestamp": "2025-07-24T04:40:40.948Z"}, {"level": "SUCCESS", "message": "User 1 initialized", "data": {"email": "<EMAIL>", "username": "testuseriplqm1"}, "timestamp": "2025-07-24T04:40:40.951Z"}, {"level": "SUCCESS", "message": "User 2 initialized", "data": {"email": "<EMAIL>", "username": "testuserseteo0"}, "timestamp": "2025-07-24T04:40:40.952Z"}, {"level": "INFO", "message": "Starting: Step 2: Run Parallel User Flows", "data": null, "timestamp": "2025-07-24T04:40:40.953Z"}, {"level": "INFO", "message": "User 1: Starting user flow", "data": null, "timestamp": "2025-07-24T04:40:40.953Z"}, {"level": "INFO", "message": "User 1: Registering user", "data": null, "timestamp": "2025-07-24T04:40:40.953Z"}, {"level": "INFO", "message": "User 2: Starting user flow", "data": null, "timestamp": "2025-07-24T04:40:40.955Z"}, {"level": "INFO", "message": "User 2: Registering user", "data": null, "timestamp": "2025-07-24T04:40:40.955Z"}, {"level": "INFO", "message": "User 1: User registered successfully", "data": {"userId": "cd3c840d-c590-4095-90fe-e685c2f19d28", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:40:41.060Z"}, {"level": "INFO", "message": "User 1: Logging in user", "data": null, "timestamp": "2025-07-24T04:40:41.061Z"}, {"level": "INFO", "message": "User 2: User registered successfully", "data": {"userId": "d771f846-1ece-43f5-adc9-106c90c6f110", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:40:41.063Z"}, {"level": "INFO", "message": "User 2: Logging in user", "data": null, "timestamp": "2025-07-24T04:40:41.063Z"}, {"level": "INFO", "message": "User 1: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:40:41.124Z"}, {"level": "INFO", "message": "User 1: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:40:41.124Z"}, {"level": "INFO", "message": "User 2: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:40:41.129Z"}, {"level": "INFO", "message": "User 2: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:40:41.129Z"}, {"level": "INFO", "message": "User 1: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:40:41.144Z"}, {"level": "INFO", "message": "User 1: Updating profile", "data": null, "timestamp": "2025-07-24T04:40:41.144Z"}, {"level": "INFO", "message": "User 2: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:40:41.145Z"}, {"level": "INFO", "message": "User 2: Updating profile", "data": null, "timestamp": "2025-07-24T04:40:41.146Z"}, {"level": "INFO", "message": "User 1: Profile updated successfully", "data": {"username": "updated1s7x5i"}, "timestamp": "2025-07-24T04:40:41.172Z"}, {"level": "INFO", "message": "User 1: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:40:41.173Z"}, {"level": "INFO", "message": "User 2: Profile updated successfully", "data": {"username": "updatedtfxij0"}, "timestamp": "2025-07-24T04:40:41.176Z"}, {"level": "INFO", "message": "User 2: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:40:41.176Z"}, {"level": "INFO", "message": "User 1: Assessment submitted successfully", "data": {"jobId": "448a078c-a883-4b9e-a17c-656fd9f89994", "status": "queued"}, "timestamp": "2025-07-24T04:40:41.296Z"}, {"level": "INFO", "message": "User 1: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:40:41.296Z"}, {"level": "INFO", "message": "User 2: Assessment submitted successfully", "data": {"jobId": "7bfa60ff-c58d-477e-8f9e-a56af0545174", "status": "queued"}, "timestamp": "2025-07-24T04:40:41.301Z"}, {"level": "INFO", "message": "User 2: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:40:41.302Z"}, {"level": "INFO", "message": "User 1: Assessment completion notification received", "data": {"jobId": "448a078c-a883-4b9e-a17c-656fd9f89994", "resultId": "4b1ed189-5de5-4ad4-8356-3382a9e2544f"}, "timestamp": "2025-07-24T04:41:08.349Z"}, {"level": "INFO", "message": "User 1: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:41:08.350Z"}, {"level": "INFO", "message": "User 1: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:41:08.350Z"}, {"level": "INFO", "message": "User 2: Assessment completion notification received", "data": {"jobId": "7bfa60ff-c58d-477e-8f9e-a56af0545174", "resultId": "2fefcb15-2724-4589-98f1-81c80a9f7651"}, "timestamp": "2025-07-24T04:41:14.262Z"}, {"level": "INFO", "message": "User 2: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:41:14.262Z"}, {"level": "INFO", "message": "User 2: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:41:14.263Z"}, {"level": "INFO", "message": "User 1: Profile persona retrieved successfully", "data": {"archetype": "The Structured Innovator", "careerCount": 4}, "timestamp": "2025-07-24T04:41:14.394Z"}, {"level": "INFO", "message": "User 1: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:41:14.395Z"}, {"level": "INFO", "message": "User 2: Profile persona retrieved successfully", "data": {"archetype": "The Meticulous Architect", "careerCount": 4}, "timestamp": "2025-07-24T04:41:20.291Z"}, {"level": "INFO", "message": "User 2: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:41:20.292Z"}, {"level": "INFO", "message": "User 1: Chatbot conversation created", "data": {"conversationId": "902e843f-e4b1-4cf9-8fba-b577d96900c6"}, "timestamp": "2025-07-24T04:41:22.294Z"}, {"level": "INFO", "message": "User 2: Chatbot conversation created", "data": {"conversationId": "c8ed2d30-c066-485e-b788-c1eded4c334f"}, "timestamp": "2025-07-24T04:41:24.683Z"}, {"level": "INFO", "message": "User 1: Message 1 sent and responded", "data": null, "timestamp": "2025-07-24T04:41:29.383Z"}, {"level": "INFO", "message": "User 2: Message 1 sent and responded", "data": null, "timestamp": "2025-07-24T04:41:31.513Z"}, {"level": "INFO", "message": "User 2: <PERSON><PERSON><PERSON> test failed", "data": null, "timestamp": "2025-07-24T04:41:36.142Z"}, {"level": "INFO", "message": "User 2: User flow failed: Request failed with status code 503", "data": null, "timestamp": "2025-07-24T04:41:36.142Z"}, {"level": "INFO", "message": "User 1: Message 2 sent and responded", "data": null, "timestamp": "2025-07-24T04:41:38.056Z"}, {"level": "INFO", "message": "User 1: Chatbot interaction completed successfully", "data": null, "timestamp": "2025-07-24T04:41:38.570Z"}, {"level": "INFO", "message": "User 1: Cleaning up test account", "data": null, "timestamp": "2025-07-24T04:41:38.571Z"}, {"level": "INFO", "message": "User 1: Test account cleaned up successfully", "data": null, "timestamp": "2025-07-24T04:41:38.591Z"}, {"level": "INFO", "message": "User 1: User flow completed successfully", "data": null, "timestamp": "2025-07-24T04:41:38.592Z"}, {"level": "SUCCESS", "message": "User 1 flow completed successfully", "data": null, "timestamp": "2025-07-24T04:41:38.592Z"}, {"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:316:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:129:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:74:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:41:38.592Z"}, {"level": "INFO", "message": "Parallel execution completed: 1 success, 1 failures", "data": null, "timestamp": "2025-07-24T04:41:38.593Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:41:38.593Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:41:38.593Z"}, {"level": "INFO", "message": "Disconnected all users from services", "data": null, "timestamp": "2025-07-24T04:41:38.595Z"}]}