# =============================================================================
# Auth Service - Docker Environment Configuration
# =============================================================================

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=3001
NODE_ENV=production

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=secret-passworrd
DB_DIALECT=postgres
DB_SCHEMA=auth

# Database connection pool settings for high concurrency
DB_POOL_MAX=75
DB_POOL_MIN=5
DB_POOL_ACQUIRE=60000
DB_POOL_IDLE=30000
DB_POOL_EVICT=10000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret (MUST be the same across all services for token verification)
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
JWT_EXPIRES_IN=7d

# Internal Service Communication Key
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Bcrypt Configuration
BCRYPT_ROUNDS=10

# =============================================================================
# BATCH PROCESSING CONFIGURATION
# =============================================================================
# User registration batch processing settings
AUTH_BATCH_MAX_SIZE=20
AUTH_BATCH_TIMEOUT=1500
AUTH_BATCH_MAX_QUEUE_SIZE=500

# =============================================================================
# TOKEN CONFIGURATION
# =============================================================================
# Default token balance for new users
DEFAULT_TOKEN_BALANCE=3

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FILE=logs/auth-service.log

# Performance Optimization Configuration
ASYNC_LAST_LOGIN=true
ENABLE_QUERY_CACHE=true
ENABLE_PERFORMANCE_MONITORING=true

# Redis Configuration (Docker)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis123
REDIS_DB=0
REDIS_KEY_PREFIX=atma:auth:

# Cache Configuration
CACHE_TTL_USER=3600
CACHE_TTL_JWT=1800
CACHE_TTL_SESSION=7200
ENABLE_CACHE=true
ENABLE_USER_CACHE=true
USER_CACHE_MAX_SIZE=1000
