# =================================
# ATMA Documentation Service Configuration
# =================================

# Server Configuration
NODE_ENV=development
PORT=80

# Documentation Configuration
# The documentation service is a static file server
# serving the built documentation files

# Logging Configuration
LOG_LEVEL=info

# Security Configuration
# Since this is a static file server, minimal configuration is needed
# CORS is handled by the web server (nginx/apache in production)

# Development Configuration
ENABLE_REQUEST_LOGGING=true

# Production Configuration (uncomment for production)
# NODE_ENV=production
# LOG_LEVEL=warn
# ENABLE_REQUEST_LOGGING=false
