{"testName": "dual-user-test-2025-07-24T04-33-09", "startTime": "2025-07-24T04:33:09.858Z", "endTime": "2025-07-24T04:33:10.069Z", "duration": 211, "results": {"passed": 2, "failed": 4, "skipped": 0, "total": 6}, "successRate": 33.33, "errors": [{"level": "ERROR", "message": "User 1 flow failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:90:22)\n    at async DualUserTest.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:217:24)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:114:7)\n    at async Promise.allSettled (index 0)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:68:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "timestamp": "2025-07-24T04:33:10.066Z"}, {"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:90:22)\n    at async DualUserTest.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:217:24)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:114:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:68:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "timestamp": "2025-07-24T04:33:10.067Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "2 user flows failed", "stack": "Error: 2 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:88:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)"}, "timestamp": "2025-07-24T04:33:10.067Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "2 user flows failed", "stack": "Error: 2 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:88:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)"}, "timestamp": "2025-07-24T04:33:10.067Z"}], "logs": [{"level": "INFO", "message": "Starting Dual User E2E Test with 2 users", "data": null, "timestamp": "2025-07-24T04:33:09.858Z"}, {"level": "INFO", "message": "Starting: Step 1: Initialize Users", "data": null, "timestamp": "2025-07-24T04:33:09.859Z"}, {"level": "SUCCESS", "message": "User 1 initialized", "data": {"email": "<EMAIL>", "username": "testusermj48ys"}, "timestamp": "2025-07-24T04:33:09.860Z"}, {"level": "SUCCESS", "message": "User 2 initialized", "data": {"email": "<EMAIL>", "username": "testuser27o8l3"}, "timestamp": "2025-07-24T04:33:09.860Z"}, {"level": "INFO", "message": "Starting: Step 2: Run Parallel User Flows", "data": null, "timestamp": "2025-07-24T04:33:09.860Z"}, {"level": "INFO", "message": "User 1: Starting user flow", "data": null, "timestamp": "2025-07-24T04:33:09.861Z"}, {"level": "INFO", "message": "User 1: Registering user", "data": null, "timestamp": "2025-07-24T04:33:09.861Z"}, {"level": "INFO", "message": "User 2: Starting user flow", "data": null, "timestamp": "2025-07-24T04:33:09.861Z"}, {"level": "INFO", "message": "User 2: Registering user", "data": null, "timestamp": "2025-07-24T04:33:09.861Z"}, {"level": "INFO", "message": "User 1: User registered successfully", "data": {"userId": "2b51d7de-ac4b-45d8-9bf3-3c35f43c5de4", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:33:09.926Z"}, {"level": "INFO", "message": "User 1: Logging in user", "data": null, "timestamp": "2025-07-24T04:33:09.927Z"}, {"level": "INFO", "message": "User 2: User registered successfully", "data": {"userId": "ff65e106-f512-49e5-81b0-5731b5c89180", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:33:09.933Z"}, {"level": "INFO", "message": "User 2: Logging in user", "data": null, "timestamp": "2025-07-24T04:33:09.933Z"}, {"level": "INFO", "message": "User 1: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:33:09.987Z"}, {"level": "INFO", "message": "User 1: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:33:09.987Z"}, {"level": "INFO", "message": "User 2: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:33:09.996Z"}, {"level": "INFO", "message": "User 2: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:33:09.997Z"}, {"level": "INFO", "message": "User 1: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:33:10.007Z"}, {"level": "INFO", "message": "User 1: Updating profile", "data": null, "timestamp": "2025-07-24T04:33:10.008Z"}, {"level": "INFO", "message": "User 2: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:33:10.017Z"}, {"level": "INFO", "message": "User 2: Updating profile", "data": null, "timestamp": "2025-07-24T04:33:10.017Z"}, {"level": "INFO", "message": "User 1: Profile updated successfully", "data": {"username": "updatedzij4x6"}, "timestamp": "2025-07-24T04:33:10.039Z"}, {"level": "INFO", "message": "User 1: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:33:10.039Z"}, {"level": "INFO", "message": "User 2: Profile updated successfully", "data": {"username": "updatedmdh8pl"}, "timestamp": "2025-07-24T04:33:10.041Z"}, {"level": "INFO", "message": "User 2: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:33:10.041Z"}, {"level": "INFO", "message": "User 1: Assessment submission failed", "data": null, "timestamp": "2025-07-24T04:33:10.060Z"}, {"level": "INFO", "message": "User 1: User flow failed: Request failed with status code 400", "data": null, "timestamp": "2025-07-24T04:33:10.060Z"}, {"level": "INFO", "message": "User 2: Assessment submission failed", "data": null, "timestamp": "2025-07-24T04:33:10.066Z"}, {"level": "INFO", "message": "User 2: User flow failed: Request failed with status code 400", "data": null, "timestamp": "2025-07-24T04:33:10.066Z"}, {"level": "ERROR", "message": "User 1 flow failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:90:22)\n    at async DualUserTest.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:217:24)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:114:7)\n    at async Promise.allSettled (index 0)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:68:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "timestamp": "2025-07-24T04:33:10.066Z"}, {"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:90:22)\n    at async DualUserTest.submitAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:217:24)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:114:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:68:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "timestamp": "2025-07-24T04:33:10.067Z"}, {"level": "INFO", "message": "Parallel execution completed: 0 success, 2 failures", "data": null, "timestamp": "2025-07-24T04:33:10.067Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "2 user flows failed", "stack": "Error: 2 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:88:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)"}, "timestamp": "2025-07-24T04:33:10.067Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "2 user flows failed", "stack": "Error: 2 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:88:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)\n    at async TestRunner.runSingleTest (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:89:7)\n    at async TestRunner.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:36:11)\n    at async TestRunner.runWithConfig (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\test-runner.js:201:12)"}, "timestamp": "2025-07-24T04:33:10.067Z"}, {"level": "INFO", "message": "Disconnected all users from services", "data": null, "timestamp": "2025-07-24T04:33:10.069Z"}]}