{"testName": "dual-user-test-2025-07-24T04-38-58", "startTime": "2025-07-24T04:38:58.964Z", "endTime": "2025-07-24T04:39:51.932Z", "duration": 52968, "results": {"passed": 3, "failed": 3, "skipped": 0, "total": 6}, "successRate": 50, "errors": [{"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:316:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:129:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:74:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:39:51.930Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:39:51.930Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:39:51.930Z"}], "logs": [{"level": "INFO", "message": "Starting Dual User E2E Test with 2 users", "data": null, "timestamp": "2025-07-24T04:38:58.964Z"}, {"level": "INFO", "message": "Starting: Step 1: Initialize Users", "data": null, "timestamp": "2025-07-24T04:38:58.966Z"}, {"level": "SUCCESS", "message": "User 1 initialized", "data": {"email": "<EMAIL>", "username": "testuserl8kaho"}, "timestamp": "2025-07-24T04:38:58.978Z"}, {"level": "SUCCESS", "message": "User 2 initialized", "data": {"email": "<EMAIL>", "username": "testuser2nvik4"}, "timestamp": "2025-07-24T04:38:58.985Z"}, {"level": "INFO", "message": "Starting: Step 2: Run Parallel User Flows", "data": null, "timestamp": "2025-07-24T04:38:58.988Z"}, {"level": "INFO", "message": "User 1: Starting user flow", "data": null, "timestamp": "2025-07-24T04:38:58.990Z"}, {"level": "INFO", "message": "User 1: Registering user", "data": null, "timestamp": "2025-07-24T04:38:58.992Z"}, {"level": "INFO", "message": "User 2: Starting user flow", "data": null, "timestamp": "2025-07-24T04:38:58.995Z"}, {"level": "INFO", "message": "User 2: Registering user", "data": null, "timestamp": "2025-07-24T04:38:58.995Z"}, {"level": "INFO", "message": "User 1: User registered successfully", "data": {"userId": "ed4375db-f5b4-4fd4-bcde-07a9b442b568", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:38:59.101Z"}, {"level": "INFO", "message": "User 1: Logging in user", "data": null, "timestamp": "2025-07-24T04:38:59.101Z"}, {"level": "INFO", "message": "User 2: User registered successfully", "data": {"userId": "b6f1b15c-c38f-47bc-9f32-b4a7226c17f3", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:38:59.103Z"}, {"level": "INFO", "message": "User 2: Logging in user", "data": null, "timestamp": "2025-07-24T04:38:59.103Z"}, {"level": "INFO", "message": "User 1: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:38:59.165Z"}, {"level": "INFO", "message": "User 1: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:38:59.166Z"}, {"level": "INFO", "message": "User 2: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:38:59.171Z"}, {"level": "INFO", "message": "User 2: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:38:59.171Z"}, {"level": "INFO", "message": "User 1: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:38:59.185Z"}, {"level": "INFO", "message": "User 1: Updating profile", "data": null, "timestamp": "2025-07-24T04:38:59.185Z"}, {"level": "INFO", "message": "User 2: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:38:59.186Z"}, {"level": "INFO", "message": "User 2: Updating profile", "data": null, "timestamp": "2025-07-24T04:38:59.186Z"}, {"level": "INFO", "message": "User 2: Profile updated successfully", "data": {"username": "updatedse2mgo"}, "timestamp": "2025-07-24T04:38:59.213Z"}, {"level": "INFO", "message": "User 2: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:38:59.214Z"}, {"level": "INFO", "message": "User 1: Profile updated successfully", "data": {"username": "updatedw8de8y"}, "timestamp": "2025-07-24T04:38:59.216Z"}, {"level": "INFO", "message": "User 1: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:38:59.216Z"}, {"level": "INFO", "message": "User 2: Assessment submitted successfully", "data": {"jobId": "cd25334b-4a2f-4913-b04b-54bc2d67f86d", "status": "queued"}, "timestamp": "2025-07-24T04:38:59.320Z"}, {"level": "INFO", "message": "User 2: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:38:59.321Z"}, {"level": "INFO", "message": "User 1: Assessment submitted successfully", "data": {"jobId": "afdc65ea-8d47-4f67-b095-0e893f4d539b", "status": "queued"}, "timestamp": "2025-07-24T04:38:59.324Z"}, {"level": "INFO", "message": "User 1: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:38:59.324Z"}, {"level": "INFO", "message": "User 1: Assessment completion notification received", "data": {"jobId": "afdc65ea-8d47-4f67-b095-0e893f4d539b", "resultId": "ad97f0b4-f597-4c79-afc0-22853d5623ba"}, "timestamp": "2025-07-24T04:39:24.091Z"}, {"level": "INFO", "message": "User 1: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:39:24.092Z"}, {"level": "INFO", "message": "User 1: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:39:24.092Z"}, {"level": "INFO", "message": "User 1: Profile persona retrieved successfully", "data": {"archetype": "The Methodical Investigator", "careerCount": 4}, "timestamp": "2025-07-24T04:39:30.130Z"}, {"level": "INFO", "message": "User 1: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:39:30.130Z"}, {"level": "INFO", "message": "User 2: Assessment completion notification received", "data": {"jobId": "cd25334b-4a2f-4913-b04b-54bc2d67f86d", "resultId": "996f5d73-6438-4285-a865-ccfa6d664961"}, "timestamp": "2025-07-24T04:39:30.136Z"}, {"level": "INFO", "message": "User 2: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:39:30.137Z"}, {"level": "INFO", "message": "User 2: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:39:30.138Z"}, {"level": "INFO", "message": "User 1: Chatbot conversation created", "data": {"conversationId": "a5e3bd4f-c5cf-4552-b136-2be610559d56"}, "timestamp": "2025-07-24T04:39:35.430Z"}, {"level": "INFO", "message": "User 2: Profile persona retrieved successfully", "data": {"archetype": "The Visionary Architect", "careerCount": 4}, "timestamp": "2025-07-24T04:39:36.161Z"}, {"level": "INFO", "message": "User 2: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:39:36.161Z"}, {"level": "INFO", "message": "User 2: Chatbot conversation created", "data": {"conversationId": "f230d160-1e14-4429-be41-4c7a73ff688f"}, "timestamp": "2025-07-24T04:39:40.981Z"}, {"level": "INFO", "message": "User 1: Message 1 sent and responded", "data": null, "timestamp": "2025-07-24T04:39:42.039Z"}, {"level": "INFO", "message": "User 2: Message 1 sent and responded", "data": null, "timestamp": "2025-07-24T04:39:47.182Z"}, {"level": "INFO", "message": "User 1: Message 2 sent and responded", "data": null, "timestamp": "2025-07-24T04:39:49.140Z"}, {"level": "INFO", "message": "User 1: Chatbot interaction completed successfully", "data": null, "timestamp": "2025-07-24T04:39:49.644Z"}, {"level": "INFO", "message": "User 1: Cleaning up test account", "data": null, "timestamp": "2025-07-24T04:39:49.645Z"}, {"level": "INFO", "message": "User 1: Test account cleaned up successfully", "data": null, "timestamp": "2025-07-24T04:39:49.671Z"}, {"level": "INFO", "message": "User 1: User flow completed successfully", "data": null, "timestamp": "2025-07-24T04:39:49.671Z"}, {"level": "INFO", "message": "User 2: <PERSON><PERSON><PERSON> test failed", "data": null, "timestamp": "2025-07-24T04:39:51.929Z"}, {"level": "INFO", "message": "User 2: User flow failed: Request failed with status code 503", "data": null, "timestamp": "2025-07-24T04:39:51.929Z"}, {"level": "SUCCESS", "message": "User 1 flow completed successfully", "data": null, "timestamp": "2025-07-24T04:39:51.930Z"}, {"level": "ERROR", "message": "User 2 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:316:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:129:7)\n    at async Promise.allSettled (index 1)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:74:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:39:51.930Z"}, {"level": "INFO", "message": "Parallel execution completed: 1 success, 1 failures", "data": null, "timestamp": "2025-07-24T04:39:51.930Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:39:51.930Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:39:51.930Z"}, {"level": "INFO", "message": "Disconnected all users from services", "data": null, "timestamp": "2025-07-24T04:39:51.932Z"}]}