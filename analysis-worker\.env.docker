# Analysis Worker - Docker Environment Configuration
# Environment for Production
NODE_ENV=production

# Worker Identification (will be overridden by docker-compose for each instance)
WORKER_ID=worker-default

# RabbitMQ Configuration (Docker)
RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin123
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process
DEAD_LETTER_QUEUE=assessment_analysis_dlq

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Google Generative AI Configuration
GOOGLE_AI_API_KEY=AIzaSyAH2hc8NyXkiuiPfib63bAZgzV1t7m3rbc
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.4

# Mock AI Configuration - DISABLED FOR PRODUCTION
USE_MOCK_MODEL=false

# Token Counting Configuration
ENABLE_TOKEN_COUNTING=true
TOKEN_USAGE_RETENTION_DAYS=30
INPUT_TOKEN_PRICE_PER_1K=0.30
OUTPUT_TOKEN_PRICE_PER_1K=2.50
TOKEN_COUNT_TIMEOUT=5000
ENABLE_TOKEN_COUNT_FALLBACK=true

# Internal Service Communication Key
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Service URLs (Docker container names)
ARCHIVE_SERVICE_URL=http://archive-service:3002
NOTIFICATION_SERVICE_URL=http://notification-service:3005
ASSESSMENT_SERVICE_URL=http://assessment-service:3003

# Worker Configuration
WORKER_CONCURRENCY=5
MAX_RETRIES=3
RETRY_DELAY=5000
PROCESSING_TIMEOUT=1800000
HEARTBEAT_INTERVAL=300000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/analysis-worker.log

# Database Optimization Configuration
DB_BATCH_SIZE=10
DB_BATCH_INTERVAL=5000
HTTP_KEEP_ALIVE=true
HTTP_MAX_SOCKETS=50
HTTP_MAX_FREE_SOCKETS=10

# Rate Limiting Configuration
RATE_LIMIT_USER_HOUR=5
RATE_LIMIT_IP_HOUR=20
RATE_LIMIT_GLOBAL_MINUTE=100

# Job Deduplication Configuration
JOB_CACHE_RETENTION_MS=3600000
MAX_JOB_CACHE_SIZE=10000

# Audit Logging Configuration
AUDIT_LOG_DIR=logs/audit
AUDIT_ENCRYPTION_KEY=your_audit_encryption_key_here
AUDIT_RETENTION_DAYS=2555

# Token Refund Configuration
ENABLE_TOKEN_REFUND=true
REFUND_PROCESSING_INTERVAL=5000
