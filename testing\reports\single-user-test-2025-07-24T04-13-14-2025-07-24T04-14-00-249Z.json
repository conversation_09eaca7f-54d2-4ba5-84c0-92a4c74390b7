{"testName": "single-user-test-2025-07-24T04-13-14", "startTime": "2025-07-24T04:13:14.973Z", "endTime": "2025-07-24T04:14:00.248Z", "duration": 45275, "results": {"passed": 9, "failed": 2, "skipped": 0, "total": 11}, "successRate": 81.82, "errors": [{"level": "ERROR", "message": "<PERSON><PERSON>bot test failed", "error": {"message": "Failed to send message 1", "stack": "Error: Failed to send message 1\n    at SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:247:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)"}, "timestamp": "2025-07-24T04:14:00.245Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Failed to send message 1", "stack": "Error: Failed to send message 1\n    at SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:247:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)"}, "timestamp": "2025-07-24T04:14:00.246Z"}], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-24T04:13:14.974Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-24T04:13:14.976Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuser2pd1ya"}, "timestamp": "2025-07-24T04:13:14.978Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-24T04:13:14.979Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "ae25d446-b040-4b80-a843-55a6f7c154c5", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:13:15.111Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-24T04:13:15.112Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-24T04:13:15.195Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-24T04:13:15.196Z"}, {"level": "SUCCESS", "message": "WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:13:15.221Z"}, {"level": "INFO", "message": "Starting: Step 5: Update Profile", "data": null, "timestamp": "2025-07-24T04:13:15.221Z"}, {"level": "SUCCESS", "message": "Profile updated successfully", "data": {"username": "updatedsd66gk"}, "timestamp": "2025-07-24T04:13:15.256Z"}, {"level": "INFO", "message": "Starting: Step 6: Submit Assessment", "data": null, "timestamp": "2025-07-24T04:13:15.257Z"}, {"level": "SUCCESS", "message": "Assessment submitted successfully", "data": {"jobId": "4fce9a0d-e143-4752-aa28-90dfedc5db5d", "status": "queued"}, "timestamp": "2025-07-24T04:13:15.397Z"}, {"level": "INFO", "message": "Starting: Step 7: Wait for WebSocket Notification", "data": null, "timestamp": "2025-07-24T04:13:15.398Z"}, {"level": "INFO", "message": "Waiting for assessment completion notification...", "data": null, "timestamp": "2025-07-24T04:13:15.398Z"}, {"level": "SUCCESS", "message": "Assessment completion notification received", "data": {"jobId": "4fce9a0d-e143-4752-aa28-90dfedc5db5d", "resultId": "e86815af-799a-4809-8157-d1a2c1d630ff"}, "timestamp": "2025-07-24T04:13:42.622Z"}, {"level": "INFO", "message": "Starting: Step 8: Get Profile Persona", "data": null, "timestamp": "2025-07-24T04:13:42.622Z"}, {"level": "INFO", "message": "Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:13:42.622Z"}, {"level": "SUCCESS", "message": "Profile persona retrieved successfully", "data": {"archetype": "The Principled Executor", "careerCount": 4}, "timestamp": "2025-07-24T04:13:48.657Z"}, {"level": "INFO", "message": "Starting: Step 9: <PERSON>", "data": null, "timestamp": "2025-07-24T04:13:48.657Z"}, {"level": "SUCCESS", "message": "Chatbot conversation created", "data": {"conversationId": "693dc371-eee6-4876-af8d-c6eabe67e9fa"}, "timestamp": "2025-07-24T04:13:53.995Z"}, {"level": "INFO", "message": "Sending message 1/5", "data": null, "timestamp": "2025-07-24T04:13:53.995Z"}, {"level": "ERROR", "message": "<PERSON><PERSON>bot test failed", "error": {"message": "Failed to send message 1", "stack": "Error: Failed to send message 1\n    at SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:247:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)"}, "timestamp": "2025-07-24T04:14:00.245Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Failed to send message 1", "stack": "Error: Failed to send message 1\n    at SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:247:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)"}, "timestamp": "2025-07-24T04:14:00.246Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-24T04:14:00.248Z"}]}