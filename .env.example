# This is a template for your environment variables.
# Copy this file to .env and fill in the actual values.
# Do NOT commit the .env file with your actual secrets to version control.

# --- PostgreSQL Database ---
POSTGRES_DB=atma_db
POSTGRES_USER=atma_user
POSTGRES_PASSWORD=password_goes_here

# --- RabbitMQ Message Broker ---
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=password_goes_here

# --- Redis Cache ---
REDIS_PASSWORD=password_goes_here

# --- Application Secrets ---
# Use a long, random string for JWT_SECRET (e.g., from a password generator)
JWT_SECRET=JWT_SECRET_GOES_HERE
INTERNAL_SERVICE_KEY=INTERNAL_SERVICE_KEY_GOES_HERE
AUDIT_ENCRYPTION_KEY=AUDIT_ENCRYPTION_KEY_GOES_HERE

# --- Google AI ---
# Your actual Google AI API key
GOOGLE_AI_API_KEY=GOOGLE_AI_API_KEY_GOES_HERE

# --- Feature Flags ---
# Set to 'true' to use the mock AI model for testing/development,
# or 'false' to use the real Google AI model in production.
USE_MOCK_MODEL=false

# --- OpenRouter AI ---
# Your OpenRouter API key for AI conversations
# Get this from https://openrouter.ai/keys
OPENROUTER_API_KEY=OPENROUTER_API_KEY_GOES_HERE

# --- Chatbot Configuration ---
# OpenRouter API Configuration
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=qwen/qwen3-235b-a22b-07-25:free
FALLBACK_MODEL=meta-llama/llama-3.2-3b-instruct:free
EMERGENCY_FALLBACK_MODEL=openai/gpt-4o-mini
USE_FREE_MODELS_ONLY=true
MAX_TOKENS=1000
TEMPERATURE=0.3
OPENROUTER_TIMEOUT=45000

# Rate Limiting Configuration
RATE_LIMIT_CONVERSATIONS_PER_DAY=100
FREE_MODEL_RATE_LIMIT_PER_MINUTE=20
MAX_CONVERSATION_HISTORY_TOKENS=6000

# General Chatbot Settings
MAX_MESSAGE_LENGTH=4000

# --- Cloudflare Tunnel ---
# Your Cloudflare Tunnel token for exposing the service
CLOUDFLARE_TUNNEL_TOKEN=CLOUDFLARE_TUNNEL_TOKEN_GOES_HERE

