# Notification Service - Docker Environment Configuration
# Server Configuration
PORT=3005
NODE_ENV=production

# JWT Configuration (same as other services)
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7

# Internal Service Authentication
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# CORS Configuration - Unlimited access
CORS_ORIGIN=*

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/notification-service.log

# Socket.IO Configuration
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# RabbitMQ Configuration for Event-Driven Architecture (Docker)
RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672
EVENTS_EXCHANGE_NAME=atma_events_exchange
EVENTS_QUEUE_NAME_NOTIFICATIONS=analysis_events_notifications
CONSUMER_PREFETCH=10
