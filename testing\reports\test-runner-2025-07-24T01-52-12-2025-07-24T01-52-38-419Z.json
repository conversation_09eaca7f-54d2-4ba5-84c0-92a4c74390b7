{"testName": "test-runner-2025-07-24T01-52-12", "startTime": "2025-07-24T01:52:12.284Z", "endTime": "2025-07-24T01:52:38.417Z", "duration": 26133, "results": {"passed": 3, "failed": 4, "skipped": 1, "total": 8}, "successRate": 37.5, "errors": [{"level": "ERROR", "message": "Test: Single User Test", "error": {}, "timestamp": "2025-07-24T01:52:27.682Z"}, {"level": "ERROR", "message": "Test: Dual User Test", "error": {}, "timestamp": "2025-07-24T01:52:27.950Z"}, {"level": "ERROR", "message": "Test: WebSocket Test", "error": {}, "timestamp": "2025-07-24T01:52:38.164Z"}, {"level": "ERROR", "message": "Test: <PERSON><PERSON><PERSON> <PERSON>", "error": {}, "timestamp": "2025-07-24T01:52:38.414Z"}], "logs": [{"level": "INFO", "message": "Starting comprehensive E2E test suite", "data": null, "timestamp": "2025-07-24T01:52:12.284Z"}, {"level": "INFO", "message": "Starting: System Health Check", "data": null, "timestamp": "2025-07-24T01:52:12.285Z"}, {"level": "SUCCESS", "message": "System health check passed", "data": {}, "timestamp": "2025-07-24T01:52:12.327Z"}, {"level": "ERROR", "message": "Test: Single User Test", "error": {}, "timestamp": "2025-07-24T01:52:27.682Z"}, {"level": "ERROR", "message": "Test: Dual User Test", "error": {}, "timestamp": "2025-07-24T01:52:27.950Z"}, {"level": "ERROR", "message": "Test: WebSocket Test", "error": {}, "timestamp": "2025-07-24T01:52:38.164Z"}, {"level": "ERROR", "message": "Test: <PERSON><PERSON><PERSON> <PERSON>", "error": {}, "timestamp": "2025-07-24T01:52:38.414Z"}, {"level": "SKIPPED", "message": "Stress Test", "reason": "Disabled in configuration", "timestamp": "2025-07-24T01:52:38.415Z"}, {"level": "SUCCESS", "message": "Final test report generated", "data": {"totalTests": 4, "passedTests": 0, "failedTests": 4, "totalDuration": 26083, "successRate": "0.00%", "results": [{"name": "Single User Test", "status": "FAILED", "duration": 15353, "error": "websocket error"}, {"name": "Dual User Test", "status": "FAILED", "duration": 267, "error": "2 user flows failed"}, {"name": "WebSocket Test", "status": "FAILED", "duration": 10214, "error": "Authentication timeout"}, {"name": "<PERSON><PERSON><PERSON> Test", "status": "FAILED", "duration": 249, "error": "Request failed with status code 400"}]}, "timestamp": "2025-07-24T01:52:38.417Z"}, {"level": "SUCCESS", "message": "E2E Test Suite completed successfully", "data": null, "timestamp": "2025-07-24T01:52:38.417Z"}]}