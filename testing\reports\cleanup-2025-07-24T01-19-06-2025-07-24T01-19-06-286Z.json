{"testName": "cleanup-2025-07-24T01-19-06", "startTime": "2025-07-24T01:19:06.279Z", "endTime": "2025-07-24T01:19:06.285Z", "duration": 6, "results": {"passed": 2, "failed": 0, "skipped": 0, "total": 2}, "successRate": 100, "errors": [], "logs": [{"level": "INFO", "message": "Starting test data cleanup", "data": null, "timestamp": "2025-07-24T01:19:06.280Z"}, {"level": "INFO", "message": "Starting: Step 1: Clean Test Users", "data": null, "timestamp": "2025-07-24T01:19:06.280Z"}, {"level": "INFO", "message": "Test user pattern for cleanup:", "data": {"pattern": "/^testuser_\\d+_[a-z0-9]+@test.atma.local$/", "domain": "test.atma.local"}, "timestamp": "2025-07-24T01:19:06.281Z"}, {"level": "WARNING", "message": "User cleanup requires admin access - skipping for now", "data": null, "timestamp": "2025-07-24T01:19:06.281Z"}, {"level": "INFO", "message": "Starting: Step 2: Clean Test Conversations", "data": null, "timestamp": "2025-07-24T01:19:06.282Z"}, {"level": "INFO", "message": "Conversation cleanup would remove:", "data": null, "timestamp": "2025-07-24T01:19:06.282Z"}, {"level": "INFO", "message": "- Conversations with test_session metadata", "data": null, "timestamp": "2025-07-24T01:19:06.282Z"}, {"level": "INFO", "message": "- Conversations created by e2e_test", "data": null, "timestamp": "2025-07-24T01:19:06.282Z"}, {"level": "INFO", "message": "- Conversations with titles containing \"E2E Test\" or \"Stress Test\"", "data": null, "timestamp": "2025-07-24T01:19:06.282Z"}, {"level": "WARNING", "message": "Conversation cleanup requires user authentication - skipping for now", "data": null, "timestamp": "2025-07-24T01:19:06.282Z"}, {"level": "INFO", "message": "Starting: Step 3: Clean Test Results", "data": null, "timestamp": "2025-07-24T01:19:06.282Z"}, {"level": "INFO", "message": "Results cleanup would remove:", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "INFO", "message": "- Results with assessment names containing \"AI-Driven Talent Mapping\"", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "INFO", "message": "- Results from test user accounts", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "WARNING", "message": "Results cleanup requires admin access - skipping for now", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "INFO", "message": "Starting: Step 4: Clean Test Jobs", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "INFO", "message": "Jobs cleanup would remove:", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "INFO", "message": "- Jobs with assessment names containing \"AI-Driven Talent Mapping\"", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "INFO", "message": "- Jobs from test user accounts", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "WARNING", "message": "Jobs cleanup requires admin access - skipping for now", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "INFO", "message": "Starting: Step 5: Generate Cleanup Report", "data": null, "timestamp": "2025-07-24T01:19:06.283Z"}, {"level": "SUCCESS", "message": "Cleanup report generated", "data": {"timestamp": "2025-07-24T01:19:06.284Z", "cleanupCount": 0, "errorCount": 0, "recommendations": ["Implement admin cleanup endpoints for automated test data removal", "Add test data markers to enable easier identification", "Consider using separate test database for E2E tests", "Implement automatic cleanup after test completion"], "manualCleanupSteps": ["1. Connect to database directly", "2. Identify test users by email pattern: /^testuser_\\d+_[a-z0-9]+@test.atma.local$/", "3. Delete related data: conversations, results, jobs", "4. Delete test user accounts", "5. Clean up any orphaned data"]}, "timestamp": "2025-07-24T01:19:06.285Z"}, {"level": "SUCCESS", "message": "Test data cleanup completed successfully", "data": null, "timestamp": "2025-07-24T01:19:06.285Z"}]}