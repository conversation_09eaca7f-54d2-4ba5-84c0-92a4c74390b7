# Assessment Service - Docker Environment Configuration
# Server Configuration
PORT=3003
NODE_ENV=production

# JWT Configuration
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7

# RabbitMQ Configuration (Docker)
RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin123
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process

# Service URLs (Docker container names)
AUTH_SERVICE_URL=http://auth-service:3001
ARCHIVE_SERVICE_URL=http://archive-service:3002

# Token Cost Configuration
ANALYSIS_TOKEN_COST=1

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Database Configuration (Docker)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=secret-passworrd
DB_DIALECT=postgres
DB_SCHEMA=assessment
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000
DB_POOL_EVICT=5000

# Outbox Pattern Configuration
OUTBOX_PROCESSING_INTERVAL=5000
OUTBOX_BATCH_SIZE=50
OUTBOX_MAX_RETRIES=3

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/assessment-service.log

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
