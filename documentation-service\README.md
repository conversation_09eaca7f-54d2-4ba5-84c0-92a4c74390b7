# ATMA API Documentation

Documentation service sederhana untuk ATMA API Gateway menggunakan vanilla JavaScript dan Vite.

## Features

- 📋 Overview API Gateway
- 🔐 Authentication & User Management
- 👨‍💼 Admin Management
- 🎯 Assessment Service
- 📊 Archive Service
- 🔔 WebSocket Notifications
- ❌ Error Handling

## Tech Stack

- Vanilla JavaScript
- Vite
- CSS3
- Prism.js (untuk syntax highlighting)

## Getting Started

### Prerequisites

- Node.js (v16 atau lebih baru)
- npm atau yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start development server:
```bash
npm run dev
```

3. Open browser di `http://localhost:3010`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```
