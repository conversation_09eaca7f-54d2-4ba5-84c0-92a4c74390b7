# This .dockerignore is for the root directory
# Individual services have their own .dockerignore files

# Dependencies
**/node_modules
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# Build outputs
**/dist
**/build

# Logs
**/logs
**/*.log

# Environment files
**/.env*

# Test files
**/tests
**/test
**/*.test.js
**/*.spec.js

# Git
.git
.gitignore

# IDE
.vscode
.idea
**/*.swp
**/*.swo
**/*~

# OS
.DS_Store
Thumbs.db

# Documentation
README.md
*.md
docs/

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Docker
**/Dockerfile
**/.dockerignore

# Testing directory
testing/

# Temporary files
tmp/
temp/
