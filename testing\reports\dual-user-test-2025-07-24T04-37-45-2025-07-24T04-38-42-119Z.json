{"testName": "dual-user-test-2025-07-24T04-37-45", "startTime": "2025-07-24T04:37:45.973Z", "endTime": "2025-07-24T04:38:42.118Z", "duration": 56145, "results": {"passed": 3, "failed": 3, "skipped": 0, "total": 6}, "successRate": 50, "errors": [{"level": "ERROR", "message": "User 1 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:316:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:129:7)\n    at async Promise.allSettled (index 0)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:74:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:38:42.114Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:38:42.115Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:38:42.116Z"}], "logs": [{"level": "INFO", "message": "Starting Dual User E2E Test with 2 users", "data": null, "timestamp": "2025-07-24T04:37:45.973Z"}, {"level": "INFO", "message": "Starting: Step 1: Initialize Users", "data": null, "timestamp": "2025-07-24T04:37:45.975Z"}, {"level": "SUCCESS", "message": "User 1 initialized", "data": {"email": "<EMAIL>", "username": "testuser9dek63"}, "timestamp": "2025-07-24T04:37:45.979Z"}, {"level": "SUCCESS", "message": "User 2 initialized", "data": {"email": "<EMAIL>", "username": "testuserrnid2z"}, "timestamp": "2025-07-24T04:37:45.979Z"}, {"level": "INFO", "message": "Starting: Step 2: Run Parallel User Flows", "data": null, "timestamp": "2025-07-24T04:37:45.980Z"}, {"level": "INFO", "message": "User 1: Starting user flow", "data": null, "timestamp": "2025-07-24T04:37:45.980Z"}, {"level": "INFO", "message": "User 1: Registering user", "data": null, "timestamp": "2025-07-24T04:37:45.981Z"}, {"level": "INFO", "message": "User 2: Starting user flow", "data": null, "timestamp": "2025-07-24T04:37:45.982Z"}, {"level": "INFO", "message": "User 2: Registering user", "data": null, "timestamp": "2025-07-24T04:37:45.982Z"}, {"level": "INFO", "message": "User 2: User registered successfully", "data": {"userId": "40401276-6923-41f0-92d6-35add9cc7d6f", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:37:46.081Z"}, {"level": "INFO", "message": "User 2: Logging in user", "data": null, "timestamp": "2025-07-24T04:37:46.082Z"}, {"level": "INFO", "message": "User 1: User registered successfully", "data": {"userId": "29a62860-1cdc-4f01-984d-f9a9d27c4f19", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:37:46.083Z"}, {"level": "INFO", "message": "User 1: Logging in user", "data": null, "timestamp": "2025-07-24T04:37:46.083Z"}, {"level": "INFO", "message": "User 2: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:37:46.143Z"}, {"level": "INFO", "message": "User 2: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:37:46.143Z"}, {"level": "INFO", "message": "User 1: User logged in successfully", "data": null, "timestamp": "2025-07-24T04:37:46.150Z"}, {"level": "INFO", "message": "User 1: Connecting WebSocket", "data": null, "timestamp": "2025-07-24T04:37:46.150Z"}, {"level": "INFO", "message": "User 2: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:37:46.171Z"}, {"level": "INFO", "message": "User 2: Updating profile", "data": null, "timestamp": "2025-07-24T04:37:46.171Z"}, {"level": "INFO", "message": "User 1: WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:37:46.174Z"}, {"level": "INFO", "message": "User 1: Updating profile", "data": null, "timestamp": "2025-07-24T04:37:46.174Z"}, {"level": "INFO", "message": "User 1: Profile updated successfully", "data": {"username": "updatedxo8da7"}, "timestamp": "2025-07-24T04:37:46.203Z"}, {"level": "INFO", "message": "User 1: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:37:46.203Z"}, {"level": "INFO", "message": "User 2: Profile updated successfully", "data": {"username": "updated653kbe"}, "timestamp": "2025-07-24T04:37:46.206Z"}, {"level": "INFO", "message": "User 2: Submitting assessment", "data": null, "timestamp": "2025-07-24T04:37:46.206Z"}, {"level": "INFO", "message": "User 1: Assessment submitted successfully", "data": {"jobId": "567d805c-4626-4607-9da1-8226a749217c", "status": "queued"}, "timestamp": "2025-07-24T04:37:46.334Z"}, {"level": "INFO", "message": "User 1: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:37:46.334Z"}, {"level": "INFO", "message": "User 2: Assessment submitted successfully", "data": {"jobId": "ed9ba163-8d0e-4dc9-9d11-3131a28e6185", "status": "queued"}, "timestamp": "2025-07-24T04:37:46.339Z"}, {"level": "INFO", "message": "User 2: Waiting for WebSocket notification", "data": null, "timestamp": "2025-07-24T04:37:46.340Z"}, {"level": "INFO", "message": "User 2: Assessment completion notification received", "data": {"jobId": "ed9ba163-8d0e-4dc9-9d11-3131a28e6185", "resultId": "3972091c-265d-4c8d-83ce-3da245cd792c"}, "timestamp": "2025-07-24T04:38:10.359Z"}, {"level": "INFO", "message": "User 2: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:38:10.360Z"}, {"level": "INFO", "message": "User 2: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:38:10.360Z"}, {"level": "INFO", "message": "User 1: Assessment completion notification received", "data": {"jobId": "567d805c-4626-4607-9da1-8226a749217c", "resultId": "bf4dd4e0-9c1f-4ffb-9b63-cf329fb2689f"}, "timestamp": "2025-07-24T04:38:15.921Z"}, {"level": "INFO", "message": "User 1: Getting profile persona", "data": null, "timestamp": "2025-07-24T04:38:15.922Z"}, {"level": "INFO", "message": "User 1: Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:38:15.922Z"}, {"level": "INFO", "message": "User 2: Profile persona retrieved successfully", "data": {"archetype": "The Prudent Visionary Leader", "careerCount": 5}, "timestamp": "2025-07-24T04:38:16.405Z"}, {"level": "INFO", "message": "User 2: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:38:16.407Z"}, {"level": "INFO", "message": "User 1: Profile persona retrieved successfully", "data": {"archetype": "The Resilient Innovator", "careerCount": 4}, "timestamp": "2025-07-24T04:38:21.941Z"}, {"level": "INFO", "message": "User 1: Testing chatbot", "data": null, "timestamp": "2025-07-24T04:38:21.941Z"}, {"level": "INFO", "message": "User 2: Chatbot conversation created", "data": {"conversationId": "c60d87da-cb2c-498c-8754-176094562c20"}, "timestamp": "2025-07-24T04:38:21.963Z"}, {"level": "INFO", "message": "User 1: Chatbot conversation created", "data": {"conversationId": "0b2aed6a-d6b4-421d-8ef5-6e8b9c3a14e7"}, "timestamp": "2025-07-24T04:38:27.266Z"}, {"level": "INFO", "message": "User 2: Message 1 sent and responded", "data": null, "timestamp": "2025-07-24T04:38:28.429Z"}, {"level": "INFO", "message": "User 1: Message 1 sent and responded", "data": null, "timestamp": "2025-07-24T04:38:35.669Z"}, {"level": "INFO", "message": "User 1: <PERSON><PERSON><PERSON> test failed", "data": null, "timestamp": "2025-07-24T04:38:40.447Z"}, {"level": "INFO", "message": "User 1: User flow failed: Request failed with status code 503", "data": null, "timestamp": "2025-07-24T04:38:40.447Z"}, {"level": "INFO", "message": "User 2: Message 2 sent and responded", "data": null, "timestamp": "2025-07-24T04:38:41.588Z"}, {"level": "INFO", "message": "User 2: Chatbot interaction completed successfully", "data": null, "timestamp": "2025-07-24T04:38:42.094Z"}, {"level": "INFO", "message": "User 2: Cleaning up test account", "data": null, "timestamp": "2025-07-24T04:38:42.095Z"}, {"level": "INFO", "message": "User 2: Test account cleaned up successfully", "data": null, "timestamp": "2025-07-24T04:38:42.114Z"}, {"level": "INFO", "message": "User 2: User flow completed successfully", "data": null, "timestamp": "2025-07-24T04:38:42.114Z"}, {"level": "ERROR", "message": "User 1 flow failed", "error": {"message": "Request failed with status code 503", "stack": "AxiosError: Request failed with status code 503\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.sendMessage (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:142:22)\n    at async DualUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:316:29)\n    at async DualUserTest.runSingleUserFlow (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:129:7)\n    at async Promise.allSettled (index 0)\n    at async DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:74:23)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)", "response": {"error": "AI service temporarily unavailable. Please try again later.", "code": "AI_SERVICE_UNAVAILABLE"}}, "timestamp": "2025-07-24T04:38:42.114Z"}, {"level": "SUCCESS", "message": "User 2 flow completed successfully", "data": null, "timestamp": "2025-07-24T04:38:42.115Z"}, {"level": "INFO", "message": "Parallel execution completed: 1 success, 1 failures", "data": null, "timestamp": "2025-07-24T04:38:42.115Z"}, {"level": "ERROR", "message": "Parallel user flows failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:38:42.115Z"}, {"level": "ERROR", "message": "Dual User E2E Test failed", "error": {"message": "1 user flows failed", "stack": "Error: 1 user flows failed\n    at DualUserTest.runParallelUserFlows (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DualUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\dual-user-test.js:23:7)"}, "timestamp": "2025-07-24T04:38:42.116Z"}, {"level": "INFO", "message": "Disconnected all users from services", "data": null, "timestamp": "2025-07-24T04:38:42.117Z"}]}