{"testName": "single-user-test-2025-07-24T04-03-13", "startTime": "2025-07-24T04:03:13.878Z", "endTime": "2025-07-24T04:03:41.962Z", "duration": 28084, "results": {"passed": 8, "failed": 2, "skipped": 0, "total": 10}, "successRate": 80, "errors": [{"level": "ERROR", "message": "<PERSON><PERSON>bot test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:218:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-24T04:03:41.961Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:218:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-24T04:03:41.961Z"}], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-24T04:03:13.879Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-24T04:03:13.880Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuserq7gsd0"}, "timestamp": "2025-07-24T04:03:13.882Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-24T04:03:13.883Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "d6668fa0-0ed3-4e60-af34-9663b3ac831c", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:03:13.976Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-24T04:03:13.976Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-24T04:03:14.030Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-24T04:03:14.031Z"}, {"level": "SUCCESS", "message": "WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:03:14.047Z"}, {"level": "INFO", "message": "Starting: Step 5: Update Profile", "data": null, "timestamp": "2025-07-24T04:03:14.048Z"}, {"level": "SUCCESS", "message": "Profile updated successfully", "data": {"username": "updated1ouz59"}, "timestamp": "2025-07-24T04:03:14.074Z"}, {"level": "INFO", "message": "Starting: Step 6: Submit Assessment", "data": null, "timestamp": "2025-07-24T04:03:14.075Z"}, {"level": "SUCCESS", "message": "Assessment submitted successfully", "data": {"jobId": "902a4ce1-2220-465e-8f58-3237d0bc7dd2", "status": "queued"}, "timestamp": "2025-07-24T04:03:14.190Z"}, {"level": "INFO", "message": "Starting: Step 7: Wait for WebSocket Notification", "data": null, "timestamp": "2025-07-24T04:03:14.191Z"}, {"level": "INFO", "message": "Waiting for assessment completion notification...", "data": null, "timestamp": "2025-07-24T04:03:14.191Z"}, {"level": "SUCCESS", "message": "Assessment completion notification received", "data": {"jobId": "902a4ce1-2220-465e-8f58-3237d0bc7dd2", "resultId": "6f3b079e-52b3-4f5b-9e02-8a9db906f05f"}, "timestamp": "2025-07-24T04:03:35.920Z"}, {"level": "INFO", "message": "Starting: Step 8: Get Profile Persona", "data": null, "timestamp": "2025-07-24T04:03:35.920Z"}, {"level": "INFO", "message": "Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:03:35.920Z"}, {"level": "SUCCESS", "message": "Profile persona retrieved successfully", "data": {"archetype": "The Collaborative Innovator", "careerCount": 5}, "timestamp": "2025-07-24T04:03:41.949Z"}, {"level": "INFO", "message": "Starting: Step 9: <PERSON>", "data": null, "timestamp": "2025-07-24T04:03:41.949Z"}, {"level": "ERROR", "message": "<PERSON><PERSON>bot test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:218:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-24T04:03:41.961Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "Request failed with status code 500", "stack": "AxiosError: Request failed with status code 500\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.createConversationFromAssessment (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:152:22)\n    at async SingleUserTest.testChatbot (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:218:28)\n    at async SingleUserTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\single-user-test.js:34:7)", "response": {"error": "Failed to create assessment conversation", "code": "CONVERSATION_CREATION_ERROR"}}, "timestamp": "2025-07-24T04:03:41.961Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-24T04:03:41.962Z"}]}