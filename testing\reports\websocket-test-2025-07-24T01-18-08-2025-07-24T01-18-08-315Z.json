{"testName": "websocket-test-2025-07-24T01-18-08", "startTime": "2025-07-24T01:18:08.267Z", "endTime": "2025-07-24T01:18:08.314Z", "duration": 47, "results": {"passed": 0, "failed": 1, "skipped": 0, "total": 1}, "successRate": 0, "errors": [{"level": "ERROR", "message": "WebSocket E2E Test failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:51:22)\n    at async WebSocketTest.setup (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\websocket-test.js:56:5)\n    at async WebSocketTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\websocket-test.js:21:7)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Email must be a valid email address"]}}}, "timestamp": "2025-07-24T01:18:08.312Z"}], "logs": [{"level": "INFO", "message": "Starting WebSocket-focused E2E Test", "data": null, "timestamp": "2025-07-24T01:18:08.268Z"}, {"level": "INFO", "message": "Starting: Step 1: Setup Test Environment", "data": null, "timestamp": "2025-07-24T01:18:08.270Z"}, {"level": "ERROR", "message": "WebSocket E2E Test failed", "error": {"message": "Request failed with status code 400", "stack": "AxiosError: Request failed with status code 400\n    at settle (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async APIClient.register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\api-client.js:51:22)\n    at async WebSocketTest.setup (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\websocket-test.js:56:5)\n    at async WebSocketTest.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\websocket-test.js:21:7)", "response": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Email must be a valid email address"]}}}, "timestamp": "2025-07-24T01:18:08.312Z"}, {"level": "INFO", "message": "Starting: Step 7: Cleanup", "data": null, "timestamp": "2025-07-24T01:18:08.313Z"}, {"level": "WARNING", "message": "Cleanup error (non-critical)", "data": {}, "timestamp": "2025-07-24T01:18:08.314Z"}]}