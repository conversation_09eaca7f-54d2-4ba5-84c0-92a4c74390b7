{"testName": "websocket-test-2025-07-24T01-52-27", "startTime": "2025-07-24T01:52:27.950Z", "endTime": "2025-07-24T01:52:38.161Z", "duration": 10211, "results": {"passed": 6, "failed": 2, "skipped": 0, "total": 8}, "successRate": 75, "errors": [{"level": "ERROR", "message": "WebSocket authentication test failed", "error": {"message": "Authentication timeout", "stack": "Error: Authentication timeout\n    at Timeout._onTimeout (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\websocket-client.js:92:16)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-24T01:52:38.134Z"}, {"level": "ERROR", "message": "WebSocket E2E Test failed", "error": {"message": "Authentication timeout", "stack": "Error: Authentication timeout\n    at Timeout._onTimeout (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\websocket-client.js:92:16)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-24T01:52:38.135Z"}], "logs": [{"level": "INFO", "message": "Starting WebSocket-focused E2E Test", "data": null, "timestamp": "2025-07-24T01:52:27.950Z"}, {"level": "INFO", "message": "Starting: Step 1: Setup Test Environment", "data": null, "timestamp": "2025-07-24T01:52:27.950Z"}, {"level": "SUCCESS", "message": "Test environment setup completed", "data": null, "timestamp": "2025-07-24T01:52:28.085Z"}, {"level": "INFO", "message": "Starting: Step 2: Test WebSocket Connection", "data": null, "timestamp": "2025-07-24T01:52:28.085Z"}, {"level": "SUCCESS", "message": "WebSocket connection established", "data": null, "timestamp": "2025-07-24T01:52:28.095Z"}, {"level": "SUCCESS", "message": "WebSocket connection with custom options successful", "data": null, "timestamp": "2025-07-24T01:52:28.104Z"}, {"level": "INFO", "message": "Starting: Step 3: Test WebSocket Authentication", "data": null, "timestamp": "2025-07-24T01:52:28.105Z"}, {"level": "SUCCESS", "message": "WebSocket authentication successful", "data": null, "timestamp": "2025-07-24T01:52:28.109Z"}, {"level": "SUCCESS", "message": "Invalid token properly rejected", "data": null, "timestamp": "2025-07-24T01:52:28.128Z"}, {"level": "ERROR", "message": "WebSocket authentication test failed", "error": {"message": "Authentication timeout", "stack": "Error: Authentication timeout\n    at Timeout._onTimeout (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\websocket-client.js:92:16)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-24T01:52:38.134Z"}, {"level": "ERROR", "message": "WebSocket E2E Test failed", "error": {"message": "Authentication timeout", "stack": "Error: Authentication timeout\n    at Timeout._onTimeout (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\lib\\websocket-client.js:92:16)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)"}, "timestamp": "2025-07-24T01:52:38.135Z"}, {"level": "INFO", "message": "Starting: Step 7: Cleanup", "data": null, "timestamp": "2025-07-24T01:52:38.136Z"}, {"level": "SUCCESS", "message": "Test account cleaned up", "data": null, "timestamp": "2025-07-24T01:52:38.160Z"}]}