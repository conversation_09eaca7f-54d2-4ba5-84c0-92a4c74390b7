# API Gateway Configuration - Docker Environment
NODE_ENV=production
PORT=3000

# Service URLs (Docker container names)
AUTH_SERVICE_URL=http://auth-service:3001
ASSESSMENT_SERVICE_URL=http://assessment-service:3003
ARCHIVE_SERVICE_URL=http://archive-service:3002
NOTIFICATION_SERVICE_URL=http://notification-service:3005
CHATBOT_SERVICE_URL=http://chatbot-service:3006

# Security
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Redis Configuration
REDIS_URL=redis://:redis123@redis:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS
ALLOWED_ORIGINS=*

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Health Check
HEALTH_CHECK_INTERVAL=30000
SERVICE_TIMEOUT=30000
