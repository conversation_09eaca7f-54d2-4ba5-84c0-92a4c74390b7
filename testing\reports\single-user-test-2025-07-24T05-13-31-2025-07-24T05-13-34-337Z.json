{"testName": "single-user-test-2025-07-24T05-13-31", "startTime": "2025-07-24T05:13:31.579Z", "endTime": "2025-07-24T05:13:34.336Z", "duration": 2757, "results": {"passed": 3, "failed": 2, "skipped": 0, "total": 5}, "successRate": 60, "errors": [{"level": "ERROR", "message": "WebSocket connection failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"}, "timestamp": "2025-07-24T05:13:34.335Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"}, "timestamp": "2025-07-24T05:13:34.335Z"}], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-24T05:13:31.580Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-24T05:13:31.581Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuserwfgw9s"}, "timestamp": "2025-07-24T05:13:31.583Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-24T05:13:31.584Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "5e50e962-000c-4f3e-be5a-3ef17e069390", "email": "<EMAIL>"}, "timestamp": "2025-07-24T05:13:31.716Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-24T05:13:31.716Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-24T05:13:31.777Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-24T05:13:31.777Z"}, {"level": "ERROR", "message": "WebSocket connection failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"}, "timestamp": "2025-07-24T05:13:34.335Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"}, "timestamp": "2025-07-24T05:13:34.335Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-24T05:13:34.336Z"}]}