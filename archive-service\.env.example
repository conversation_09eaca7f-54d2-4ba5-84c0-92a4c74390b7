# Server Configuration
PORT=3002
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=postgres
DB_PASSWORD=password
DB_DIALECT=postgres
DB_SCHEMA=archive
DB_MIGRATION_USER=postgres
DB_MIGRATION_PASSWORD=password

# JWT Configuration (for token verification)
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:3001

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Pagination Configuration
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/archive-service.log

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Batch Processing Configuration (UPDATED)
BATCH_MAX_SIZE=100                 # Naik dari 50 → 100
BATCH_TIMEOUT=2000                 # Tetap 2 detik
BATCH_MAX_QUEUE_SIZE=2000          # Naik dari 1000 → 2000

# Database Pool Optimization (UPDATED)
DB_POOL_MAX=75                     # Production: 75
DB_POOL_MIN=15                     # Production: 15
DB_POOL_ACQUIRE=25000              # Production: 25000
DB_POOL_IDLE=15000                 # Production: 15000
DB_POOL_EVICT=3000                 # Production: 3000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=password_goes_here
REDIS_DB=0
DISABLE_REDIS=false
