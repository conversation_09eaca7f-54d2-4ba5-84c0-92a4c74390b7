# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=secret-passworrd
DB_DIALECT=postgres
DB_SCHEMA=auth

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# Bcrypt Configuration
BCRYPT_ROUNDS=10

# Default Token Balance for New Users
DEFAULT_TOKEN_BALANCE=3

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/auth-service.log

# Performance Optimization Configuration
ASYNC_LAST_LOGIN=true
ENABLE_QUERY_CACHE=true
ENABLE_PERFORMANCE_MONITORING=true

# Database Pool Configuration (stable settings)
DB_POOL_MAX=75
DB_POOL_MIN=5
DB_POOL_ACQUIRE=60000
DB_POOL_IDLE=30000
DB_POOL_EVICT=10000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=atma:auth:

# Cache Configuration
CACHE_TTL_USER=3600
CACHE_TTL_JWT=1800
CACHE_TTL_SESSION=7200
ENABLE_CACHE=true
ENABLE_USER_CACHE=true
USER_CACHE_MAX_SIZE=1000

# Batch Processing Configuration
AUTH_BATCH_MAX_SIZE=20
AUTH_BATCH_TIMEOUT=1500
AUTH_BATCH_MAX_QUEUE_SIZE=500
