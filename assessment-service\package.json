{"name": "atma-assessment-service", "version": "1.0.0", "description": "Assessment Service for ATMA Backend - Receives and queues assessment data", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:archive": "node scripts/test-archive-integration.js", "test:archive:examples": "node examples/archive-integration-examples.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.21.2", "amqplib": "^0.10.4", "joi": "^17.13.3", "express-jwt": "^8.4.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "axios": "^1.7.9", "uuid": "^11.0.3", "morgan": "^1.10.0", "sequelize": "^6.37.5", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.1.10", "jest": "^29.7.0", "supertest": "^7.1.3", "eslint": "^8.57.0"}, "keywords": ["assessment", "queue", "rabbitmq", "microservices", "express", "nodejs"], "author": "ATMA Team", "license": "MIT"}