{"testName": "single-user-test-2025-07-24T01-52-12", "startTime": "2025-07-24T01:52:12.328Z", "endTime": "2025-07-24T01:52:27.678Z", "duration": 15350, "results": {"passed": 3, "failed": 2, "skipped": 0, "total": 5}, "successRate": 60, "errors": [{"level": "ERROR", "message": "WebSocket connection failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at ClientRequest.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:880:5)\n    at ClientRequest.emit (node:events:518:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketOnEnd (node:_http_client:542:5)"}, "timestamp": "2025-07-24T01:52:27.677Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at ClientRequest.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:880:5)\n    at ClientRequest.emit (node:events:518:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketOnEnd (node:_http_client:542:5)"}, "timestamp": "2025-07-24T01:52:27.678Z"}], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-24T01:52:12.329Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-24T01:52:12.329Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuser_w56dap"}, "timestamp": "2025-07-24T01:52:12.332Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-24T01:52:12.333Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "941eb840-2f0a-49f6-9e8e-01dcd4cfbfc4", "email": "<EMAIL>"}, "timestamp": "2025-07-24T01:52:12.590Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-24T01:52:12.590Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-24T01:52:12.661Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-24T01:52:12.661Z"}, {"level": "ERROR", "message": "WebSocket connection failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at ClientRequest.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:880:5)\n    at ClientRequest.emit (node:events:518:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketOnEnd (node:_http_client:542:5)"}, "timestamp": "2025-07-24T01:52:27.677Z"}, {"level": "ERROR", "message": "Single User E2E Test failed", "error": {"message": "websocket error", "stack": "Error: websocket error\n    at WS.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transport.js:48:37)\n    at ws.onerror (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\engine.io-client\\build\\cjs\\transports\\websocket.js:57:39)\n    at callListener (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onError (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\event-target.js:230:9)\n    at WebSocket.emit (node:events:518:28)\n    at emitErrorAndClose (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:1035:13)\n    at ClientRequest.<anonymous> (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\testing\\node_modules\\ws\\lib\\websocket.js:880:5)\n    at ClientRequest.emit (node:events:518:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketOnEnd (node:_http_client:542:5)"}, "timestamp": "2025-07-24T01:52:27.678Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-24T01:52:27.678Z"}]}