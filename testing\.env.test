# ATMA E2E Testing Configuration - Test Database
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000
TEST_TIMEOUT=30000
ASSESSMENT_TIMEOUT=300000
WEBSOCKET_TIMEOUT=10000

# Test Configuration
ENABLE_CLEANUP=true
ENABLE_DETAILED_LOGS=true
PARALLEL_USERS=2
STRESS_TEST_USERS=5

# Email Configuration for Random Generation
EMAIL_DOMAIN=example.com
USERNAME_PREFIX=testuser

# Assessment Test Data
DEFAULT_ASSESSMENT_NAME=AI-Driven Talent Mapping

# Test Database Configuration
# Use separate database for testing to avoid data conflicts
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_test_db
DB_USER=atma_user
DB_PASSWORD=secret-passworrd
DB_SCHEMA_AUTH=auth
DB_SCHEMA_ARCHIVE=archive
DB_SCHEMA_CHATBOT=chatbot

# Test Data Cleanup
AUTO_CLEANUP_ENABLED=true
CLEANUP_BATCH_SIZE=100
CLEANUP_OLDER_THAN_HOURS=24
