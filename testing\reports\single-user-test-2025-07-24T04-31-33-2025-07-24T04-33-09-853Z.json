{"testName": "single-user-test-2025-07-24T04-31-33", "startTime": "2025-07-24T04:31:33.645Z", "endTime": "2025-07-24T04:33:09.853Z", "duration": 96208, "results": {"passed": 17, "failed": 0, "skipped": 0, "total": 17}, "successRate": 100, "errors": [], "logs": [{"level": "INFO", "message": "Starting Single User E2E Test", "data": null, "timestamp": "2025-07-24T04:31:33.646Z"}, {"level": "INFO", "message": "Starting: Step 1: Generate Test Data", "data": null, "timestamp": "2025-07-24T04:31:33.646Z"}, {"level": "SUCCESS", "message": "Test data generated", "data": {"email": "<EMAIL>", "username": "testuser4w16xx"}, "timestamp": "2025-07-24T04:31:33.648Z"}, {"level": "INFO", "message": "Starting: Step 2: Register User", "data": null, "timestamp": "2025-07-24T04:31:33.648Z"}, {"level": "SUCCESS", "message": "User registered successfully", "data": {"userId": "45730cc6-14b3-46d7-b99e-1df02183c803", "email": "<EMAIL>"}, "timestamp": "2025-07-24T04:31:33.728Z"}, {"level": "INFO", "message": "Starting: Step 3: <PERSON>gin User", "data": null, "timestamp": "2025-07-24T04:31:33.728Z"}, {"level": "SUCCESS", "message": "User logged in successfully", "data": null, "timestamp": "2025-07-24T04:31:33.784Z"}, {"level": "INFO", "message": "Starting: Step 4: Connect WebSocket", "data": null, "timestamp": "2025-07-24T04:31:33.784Z"}, {"level": "SUCCESS", "message": "WebSocket connected and authenticated", "data": null, "timestamp": "2025-07-24T04:31:33.801Z"}, {"level": "INFO", "message": "Starting: Step 5: Update Profile", "data": null, "timestamp": "2025-07-24T04:31:33.801Z"}, {"level": "SUCCESS", "message": "Profile updated successfully", "data": {"username": "updateda44gcg"}, "timestamp": "2025-07-24T04:31:33.822Z"}, {"level": "INFO", "message": "Starting: Step 6: Submit Assessment", "data": null, "timestamp": "2025-07-24T04:31:33.822Z"}, {"level": "SUCCESS", "message": "Assessment submitted successfully", "data": {"jobId": "a28aef6a-7c8d-4cc4-b738-b20b724bb6a1", "status": "queued"}, "timestamp": "2025-07-24T04:31:33.962Z"}, {"level": "INFO", "message": "Starting: Step 7: Wait for WebSocket Notification", "data": null, "timestamp": "2025-07-24T04:31:33.963Z"}, {"level": "INFO", "message": "Waiting for assessment completion notification...", "data": null, "timestamp": "2025-07-24T04:31:33.963Z"}, {"level": "SUCCESS", "message": "Assessment completion notification received", "data": {"jobId": "a28aef6a-7c8d-4cc4-b738-b20b724bb6a1", "resultId": "c3f3fac1-8800-4ade-a643-aeca4cc2b716"}, "timestamp": "2025-07-24T04:32:02.214Z"}, {"level": "INFO", "message": "Starting: Step 8: Get Profile Persona", "data": null, "timestamp": "2025-07-24T04:32:02.216Z"}, {"level": "INFO", "message": "Waiting for batch processing to complete...", "data": null, "timestamp": "2025-07-24T04:32:02.216Z"}, {"level": "SUCCESS", "message": "Profile persona retrieved successfully", "data": {"archetype": "The Purposeful Achiever", "careerCount": 5}, "timestamp": "2025-07-24T04:32:08.255Z"}, {"level": "INFO", "message": "Starting: Step 9: <PERSON>", "data": null, "timestamp": "2025-07-24T04:32:08.255Z"}, {"level": "SUCCESS", "message": "Chatbot conversation created", "data": {"conversationId": "f2919011-9599-42e7-bbaa-c89c20ddbf9b"}, "timestamp": "2025-07-24T04:32:13.044Z"}, {"level": "INFO", "message": "Sending message 1/5", "data": null, "timestamp": "2025-07-24T04:32:13.044Z"}, {"level": "SUCCESS", "message": "Message 1 sent and responded", "data": {"userMessage": "Hello, I need career guidance based on my assessme...", "assistantResponse": "Hello! I'm really glad you reached out—I'd be hono..."}, "timestamp": "2025-07-24T04:32:19.020Z"}, {"level": "INFO", "message": "Sending message 2/5", "data": null, "timestamp": "2025-07-24T04:32:20.027Z"}, {"level": "SUCCESS", "message": "Message 2 sent and responded", "data": {"userMessage": "What career paths would suit my personality type?...", "assistantResponse": "I'd love to give you a clear, personalized answer ..."}, "timestamp": "2025-07-24T04:32:28.507Z"}, {"level": "INFO", "message": "Sending message 3/5", "data": null, "timestamp": "2025-07-24T04:32:29.510Z"}, {"level": "SUCCESS", "message": "Message 3 sent and responded", "data": {"userMessage": "Can you help me understand my strengths and weakne...", "assistantResponse": "Absolutely — I’d be honored to help you understand..."}, "timestamp": "2025-07-24T04:32:44.902Z"}, {"level": "INFO", "message": "Sending message 4/5", "data": null, "timestamp": "2025-07-24T04:32:45.903Z"}, {"level": "SUCCESS", "message": "Message 4 sent and responded", "data": {"userMessage": "What skills should I focus on developing?...", "assistantResponse": "Great question — and a powerful one. **Knowing whi..."}, "timestamp": "2025-07-24T04:33:01.255Z"}, {"level": "INFO", "message": "Sending message 5/5", "data": null, "timestamp": "2025-07-24T04:33:02.268Z"}, {"level": "SUCCESS", "message": "Message 5 sent and responded", "data": {"userMessage": "Thank you for the guidance!...", "assistantResponse": "You're so very welcome! 🌟  \nThank *you* for showi..."}, "timestamp": "2025-07-24T04:33:08.823Z"}, {"level": "SUCCESS", "message": "Chatbot interaction completed successfully", "data": null, "timestamp": "2025-07-24T04:33:09.832Z"}, {"level": "INFO", "message": "Starting: Step 10: Cleanup Test Account", "data": null, "timestamp": "2025-07-24T04:33:09.832Z"}, {"level": "SUCCESS", "message": "Test account cleaned up successfully", "data": null, "timestamp": "2025-07-24T04:33:09.851Z"}, {"level": "SUCCESS", "message": "Single User E2E Test completed successfully", "data": null, "timestamp": "2025-07-24T04:33:09.851Z"}, {"level": "INFO", "message": "Disconnected from services", "data": null, "timestamp": "2025-07-24T04:33:09.852Z"}]}