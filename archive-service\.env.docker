# =============================================================================
# Archive Service - Docker Environment Configuration
# =============================================================================

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=3002
NODE_ENV=production

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database Configuration (Docker)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=secret-passworrd
DB_DIALECT=postgres
DB_SCHEMA=archive

# Migration-specific database credentials
DB_MIGRATION_USER=atma_user
DB_MIGRATION_PASSWORD=secret-passworrd

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret (MUST be the same across all services for token verification)
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7

# Internal Service Communication Key
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# =============================================================================
# SERVICE URLS (Docker container names)
# =============================================================================
# Auth Service Configuration
AUTH_SERVICE_URL=http://auth-service:3001

# =============================================================================
# PAGINATION CONFIGURATION
# =============================================================================
# Default pagination settings
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100

# =============================================================================
# BATCH PROCESSING CONFIGURATION
# =============================================================================
# Maximum number of items to process in a single batch
BATCH_MAX_SIZE=50

# Maximum time to wait before processing a batch (milliseconds)
BATCH_TIMEOUT=2000

# Maximum number of items that can be queued for batch processing
BATCH_MAX_QUEUE_SIZE=1000

# Database connection pool settings for high concurrency
DB_POOL_MAX=75
DB_POOL_MIN=15
DB_POOL_ACQUIRE=25000
DB_POOL_IDLE=15000
DB_POOL_EVICT=3000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging Level (info, debug, warn, error)
LOG_LEVEL=info
LOG_FILE=logs/archive-service.log

# Redis Configuration (Docker)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis123
REDIS_DB=0
# Set to 'false' to enable Redis caching in Docker
DISABLE_REDIS=false
